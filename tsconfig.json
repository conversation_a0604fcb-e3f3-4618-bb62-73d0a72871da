{"compilerOptions": {"target": "ES2022", "experimentalDecorators": true, "useDefineForClassFields": false, "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "skipLibCheck": true, "types": ["node"], "moduleResolution": "bundler", "isolatedModules": true, "moduleDetection": "force", "allowJs": true, "jsx": "react-jsx", "paths": {"@/*": ["./*"]}, "allowImportingTsExtensions": true, "noEmit": true}}