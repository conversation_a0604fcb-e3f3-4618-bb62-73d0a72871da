import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { db } from './firebase';
import { collection, onSnapshot, doc, addDoc, updateDoc, deleteDoc, query, orderBy, writeBatch } from 'firebase/firestore';
import { Account, Transaction, AccountType, TransactionType, GeminiScanResult, Currency, Subscription, SubscriptionFrequency } from './types';
import { useDebounce } from './hooks/useDebounce';
import { EXPENSE_CATEGORIES, INCOME_CATEGORIES } from './constants';
import { scanReceiptWithFirebase } from './services/geminiService';
import { HomeIcon, ListIcon, CameraIcon, CogIcon, PlusIcon, ArrowDownIcon, ArrowUpIcon, SearchIcon, ReceiptIcon, RepeatIcon, LogoutIcon } from './components/Icons';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { AuthScreen } from './components/AuthScreen';


function MainApp() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [currency, setCurrency] = useState<Currency>(Currency.USD);
  const [isLoading, setIsLoading] = useState(true);

  const [modal, setModal] = useState<string | null>(null);
  const [editingTransaction, setEditingTransaction] = useState<Transaction | Partial<Transaction> | null>(null);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [editingSubscription, setEditingSubscription] = useState<Subscription | null>(null);

  // Effect for fetching all data from Firestore
  useEffect(() => {
    if (!user) {
      setAccounts([]);
      setTransactions([]);
      setSubscriptions([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    
    // User settings (currency)
    const userDocUnsub = onSnapshot(doc(db, 'users', user.uid), (doc) => {
        if (doc.exists()) {
            setCurrency(doc.data().currency || Currency.USD);
        }
    });

    // Accounts
    const accountsUnsub = onSnapshot(collection(db, `users/${user.uid}/accounts`), (snapshot) => {
        const fetchedAccounts = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Account));
        setAccounts(fetchedAccounts);
    });

    // Transactions
    const transactionsQuery = query(collection(db, `users/${user.uid}/transactions`), orderBy('date', 'desc'));
    const transactionsUnsub = onSnapshot(transactionsQuery, (snapshot) => {
        const fetchedTransactions = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Transaction));
        setTransactions(fetchedTransactions);
    });
    
    // Subscriptions
    const subscriptionsUnsub = onSnapshot(collection(db, `users/${user.uid}/subscriptions`), (snapshot) => {
        const fetchedSubscriptions = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Subscription));
        setSubscriptions(fetchedSubscriptions);
        setIsLoading(false); // Consider loading finished after the largest collection is fetched
    });

    return () => {
        userDocUnsub();
        accountsUnsub();
        transactionsUnsub();
        subscriptionsUnsub();
    };
  }, [user]);
  
  // Effect to process subscriptions
  useEffect(() => {
    if (!user || subscriptions.length === 0 || isLoading) return;

    const processDueSubscriptions = async () => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const batch = writeBatch(db);
      let hasUpdates = false;

      subscriptions.forEach(sub => {
        let nextPaymentDate = new Date(sub.startDate);
        if (isNaN(nextPaymentDate.getTime())) return;
        nextPaymentDate.setHours(0, 0, 0, 0);

        let subHasBeenUpdated = false;
        let lastUpdatedPaymentDate = new Date(nextPaymentDate);

        while (nextPaymentDate <= today) {
          hasUpdates = true;
          subHasBeenUpdated = true;
          
          // 1. Create a new transaction for this payment
          const newTxDocRef = doc(collection(db, `users/${user.uid}/transactions`));
          batch.set(newTxDocRef, {
            accountId: sub.accountId,
            type: TransactionType.Expense,
            amount: sub.amount,
            category: sub.category,
            description: `(Sub) ${sub.description}`,
            date: nextPaymentDate.toISOString(),
          });
          
          // 2. Calculate the next payment date
          lastUpdatedPaymentDate = new Date(nextPaymentDate);
          if (sub.frequency === SubscriptionFrequency.Weekly) {
            nextPaymentDate.setDate(nextPaymentDate.getDate() + 7);
          } else {
            nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
          }
        }

        // 3. Update the subscription with the new start date
        if (subHasBeenUpdated) {
          const subDocRef = doc(db, `users/${user.uid}/subscriptions`, sub.id);
          batch.update(subDocRef, { startDate: nextPaymentDate.toISOString().split('T')[0] });
        }
      });
      
      if (hasUpdates) {
          try {
              await batch.commit();
          } catch (error) {
              console.error("Error processing subscriptions:", error);
          }
      }
    };
    
    processDueSubscriptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscriptions, user, isLoading]);

  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat(undefined, { style: 'currency', currency }).format(amount);
  }, [currency]);

  const calculateBalance = useCallback((accountId: string) => {
    const account = accounts.find(a => a.id === accountId);
    if (!account) return 0;
    return transactions
      .filter(t => t.accountId === accountId)
      .reduce((acc, t) => t.type === TransactionType.Income ? acc + t.amount : acc - t.amount, account.initialBalance);
  }, [accounts, transactions]);
  
  const totalBalance = useMemo(() => {
    return accounts.reduce((total, acc) => total + calculateBalance(acc.id), 0);
  }, [accounts, calculateBalance]);

  // Data Handlers
  const handleAddTransaction = async (tx: Omit<Transaction, 'id'>) => {
    await addDoc(collection(db, `users/${user!.uid}/transactions`), tx);
    setModal(null);
  };
  const handleUpdateTransaction = async (tx: Transaction) => {
    const txDocRef = doc(db, `users/${user!.uid}/transactions`, tx.id);
    const { id, ...data } = tx;
    await updateDoc(txDocRef, data);
    setEditingTransaction(null);
    setModal(null);
  };
  const handleDeleteTransaction = async (id: string) => {
    if (confirm('Are you sure you want to permanently delete this transaction?')) {
      await deleteDoc(doc(db, `users/${user!.uid}/transactions`, id));
    }
  };
  const handleAddAccount = async (acc: Omit<Account, 'id'>) => {
    await addDoc(collection(db, `users/${user!.uid}/accounts`), acc);
    setModal(null);
  };
  const handleUpdateAccount = async (acc: Account) => {
    const accDocRef = doc(db, `users/${user!.uid}/accounts`, acc.id);
    const { id, ...data } = acc;
    await updateDoc(accDocRef, data);
    setEditingAccount(null);
    setModal(null);
  };
  const handleDeleteAccount = async (id: string) => {
    if(confirm('Delete this account? This will also delete all associated transactions.')) {
        // Use a batch write to delete account and transactions atomically
        const batch = writeBatch(db);
        const accDocRef = doc(db, `users/${user!.uid}/accounts`, id);
        batch.delete(accDocRef);
        // This is inefficient for large datasets, but fine for a single user.
        // A better approach would be a Cloud Function.
        transactions.filter(t => t.accountId === id).forEach(t => {
            const txDocRef = doc(db, `users/${user!.uid}/transactions`, t.id);
            batch.delete(txDocRef);
        });
        await batch.commit();
    }
  };
  const handleAddSubscription = async (sub: Omit<Subscription, 'id'>) => {
      await addDoc(collection(db, `users/${user!.uid}/subscriptions`), sub);
      setModal(null);
  };
  const handleUpdateSubscription = async (sub: Subscription) => {
      const subDocRef = doc(db, `users/${user!.uid}/subscriptions`, sub.id);
      const { id, ...data } = sub;
      await updateDoc(subDocRef, data);
      setEditingSubscription(null);
      setModal(null);
  };
  const handleDeleteSubscription = async (id: string) => {
      if(confirm('Are you sure you want to delete this subscription?')) {
          await deleteDoc(doc(db, `users/${user!.uid}/subscriptions`, id));
      }
  };
  const handleSetCurrency = async (newCurrency: Currency) => {
      setCurrency(newCurrency);
      await updateDoc(doc(db, 'users', user!.uid), { currency: newCurrency });
  };

  const openTransactionModal = (tx?: Transaction) => {
    setEditingTransaction(tx || null);
    setModal('transaction');
  };
  const openAccountModal = (acc?: Account) => {
      setEditingAccount(acc || null);
      setModal('account');
  };
  const openSubscriptionModal = (sub?: Subscription) => {
      setEditingSubscription(sub || null);
      setModal('subscription');
  };
  
  if (isLoading) {
      return (
          <div className="min-h-screen bg-black flex items-center justify-center text-white">
              <svg className="animate-spin h-8 w-8 text-indigo-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
          </div>
      );
  }
  
  const renderScreen = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardScreen accounts={accounts} calculateBalance={calculateBalance} totalBalance={totalBalance} transactions={transactions.slice(0, 5)} openTransactionModal={openTransactionModal} formatCurrency={formatCurrency} />;
      case 'transactions':
        return <TransactionsScreen transactions={transactions} subscriptions={subscriptions} accounts={accounts} openTransactionModal={openTransactionModal} handleDeleteTransaction={handleDeleteTransaction} openSubscriptionModal={openSubscriptionModal} handleDeleteSubscription={handleDeleteSubscription} formatCurrency={formatCurrency} />;
      case 'settings':
        return <SettingsScreen accounts={accounts} openAccountModal={openAccountModal} handleDeleteAccount={handleDeleteAccount} currency={currency} setCurrency={handleSetCurrency} />;
      default:
        return <DashboardScreen accounts={accounts} calculateBalance={calculateBalance} totalBalance={totalBalance} transactions={transactions.slice(0, 5)} openTransactionModal={openTransactionModal} formatCurrency={formatCurrency} />;
    }
  };

  return (
    <div className="min-h-screen bg-black flex flex-col font-sans">
      <main className="flex-1 pb-24 overflow-y-auto">
        <div className="max-w-md mx-auto p-4">
          {renderScreen()}
        </div>
      </main>

      {modal === 'addChoice' && <AddChoiceModal onClose={() => setModal(null)} onAddTransaction={() => { setModal('transaction'); }} onAddSubscription={() => { setModal('subscription'); }} />}
      {modal === 'transaction' && <TransactionModal accounts={accounts} transaction={editingTransaction} onSave={editingTransaction?.id ? handleUpdateTransaction : handleAddTransaction} onClose={() => { setModal(null); setEditingTransaction(null); }} currency={currency} />}
      {modal === 'scan' && <ScanModal accounts={accounts} onClose={() => setModal(null)} onScanComplete={(data, accountId) => {
          const newTx: Omit<Transaction, 'id'> = {
              accountId,
              type: TransactionType.Expense,
              amount: data.totalAmount,
              category: data.category,
              description: data.merchantName,
              date: new Date(data.transactionDate).toISOString(),
          };
          setEditingTransaction(newTx); 
          setModal('transaction');
      }} />}
      {modal === 'account' && <AccountModal account={editingAccount} onSave={editingAccount ? handleUpdateAccount : handleAddAccount} onClose={() => { setModal(null); setEditingAccount(null); }} />}
      {modal === 'subscription' && <SubscriptionModal accounts={accounts} subscription={editingSubscription} onSave={editingSubscription ? handleUpdateSubscription : handleAddSubscription} onClose={() => { setModal(null); setEditingSubscription(null); }} currency={currency} />}

      <BottomNav activeTab={activeTab} setActiveTab={setActiveTab} onScanClick={() => setModal('scan')} onAddClick={() => setModal('addChoice')}/>
    </div>
  );
}

// App component orchestrates the entire application
export default function App() {
  return (
    <AuthProvider>
        <AppContent />
    </AuthProvider>
  );
}

const AppContent = () => {
    const { user, loading } = useAuth();

    if (loading) {
        return (
          <div className="min-h-screen bg-black flex items-center justify-center text-white">
              <svg className="animate-spin h-8 w-8 text-indigo-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
          </div>
        );
    }
    
    return user ? <MainApp /> : <AuthScreen />;
};

// Component Prop Types
interface DashboardScreenProps {
  accounts: Account[];
  calculateBalance: (accountId: string) => number;
  totalBalance: number;
  transactions: Transaction[];
  openTransactionModal: (tx?: Transaction) => void;
  formatCurrency: (amount: number) => string;
}
interface TransactionsScreenProps {
  transactions: Transaction[];
  subscriptions: Subscription[];
  accounts: Account[];
  openTransactionModal: (tx?: Transaction) => void;
  handleDeleteTransaction: (id: string) => void;
  openSubscriptionModal: (sub?: Subscription) => void;
  handleDeleteSubscription: (id: string) => void;
  formatCurrency: (amount: number) => string;
}
interface SettingsScreenProps {
    accounts: Account[];
    openAccountModal: (acc?: Account) => void;
    handleDeleteAccount: (id: string) => void;
    currency: Currency;
    setCurrency: (currency: Currency) => void;
}

// Dashboard Screen
const DashboardScreen: React.FC<DashboardScreenProps> = ({ accounts, calculateBalance, totalBalance, transactions, openTransactionModal, formatCurrency }) => {
  return (
      <div className="animate-fade-in">
          <header className="mb-8">
              <h1 className="text-base text-gray-400">Total Balance</h1>
              <p className="text-4xl font-bold tracking-tight text-gray-100">{formatCurrency(totalBalance)}</p>
          </header>

          <section className="mb-8">
              <h2 className="text-lg font-semibold text-gray-300 mb-3">Accounts</h2>
              <div className="space-y-3">
                  {accounts.length > 0 ? accounts.map((acc) => (
                      <div key={acc.id} className="bg-gray-900 p-4 rounded-xl border border-gray-800">
                          <div className="flex justify-between items-center">
                              <div>
                                  <p className="font-semibold text-gray-100">{acc.name}</p>
                                  <p className="text-sm text-gray-400">{acc.type}</p>
                              </div>
                              <p className="text-lg font-mono font-semibold text-gray-200">{formatCurrency(calculateBalance(acc.id))}</p>
                          </div>
                      </div>
                  )) : (
                      <p className="text-center text-gray-500 py-4">No accounts yet. Go to Settings to add one.</p>
                  )}
              </div>
          </section>

          <section>
              <h2 className="text-lg font-semibold text-gray-300 mb-3">Recent Transactions</h2>
              <div className="space-y-2">
                  {transactions.length > 0 ? transactions.map((tx) => (
                      <TransactionItem key={tx.id} transaction={tx} account={accounts.find(a => a.id === tx.accountId)} onClick={() => openTransactionModal(tx)} formatCurrency={formatCurrency} />
                  )) : (
                      <p className="text-center text-gray-500 py-4">No transactions yet.</p>
                  )}
              </div>
          </section>
      </div>
  );
};

// Transactions Screen
const TransactionsScreen: React.FC<TransactionsScreenProps> = ({ transactions, subscriptions, accounts, openTransactionModal, handleDeleteTransaction, openSubscriptionModal, handleDeleteSubscription, formatCurrency }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [view, setView] = useState<'transactions' | 'subscriptions'>('transactions');

  const filteredTransactions = useMemo(() => {
    if (!debouncedSearchQuery) {
      return transactions;
    }
    const lowercasedQuery = debouncedSearchQuery.toLowerCase();
    return transactions.filter(tx => {
      const account = accounts.find(a => a.id === tx.accountId);
      const accountName = account ? account.name.toLowerCase() : '';

      const descriptionMatch = tx.description.toLowerCase().includes(lowercasedQuery);
      const accountMatch = accountName.includes(lowercasedQuery);
      const amountMatch = tx.amount.toString().includes(lowercasedQuery);
      
      return descriptionMatch || accountMatch || amountMatch;
    });
  }, [transactions, accounts, debouncedSearchQuery]);

  const sortedSubscriptions = useMemo(() => {
    return [...subscriptions].sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
  }, [subscriptions]);

  return (
    <div className="animate-fade-in">
        <h1 className="text-2xl font-bold mb-4 text-gray-100">{view === 'transactions' ? 'All Transactions' : 'Recurring Subscriptions'}</h1>

        <div className="grid grid-cols-2 gap-2 mb-4 bg-gray-900 p-1 rounded-lg">
            <button type="button" onClick={() => setView('transactions')} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${view === 'transactions' ? 'bg-gray-700 shadow' : 'text-gray-400'}`}>Transactions</button>
            <button type="button" onClick={() => setView('subscriptions')} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${view === 'subscriptions' ? 'bg-gray-700 shadow' : 'text-gray-400'}`}>Subscriptions</button>
        </div>

        {view === 'transactions' && (
            <>
                <div className="relative mb-4">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <SearchIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="Search by amount, description, account..."
                        className="w-full bg-gray-900 border-gray-700 rounded-lg shadow-sm pl-10 pr-4 py-2 focus:ring-indigo-500 focus:border-indigo-500 text-white"
                    />
                </div>

                <div className="space-y-2">
                    {filteredTransactions.length > 0 ? filteredTransactions.map((tx) => (
                      <div key={tx.id} className="group relative">
                        <TransactionItem transaction={tx} account={accounts.find(a => a.id === tx.accountId)} onClick={() => openTransactionModal(tx)} formatCurrency={formatCurrency} />
                        <button 
                          onClick={() => handleDeleteTransaction(tx.id)}
                          className="absolute right-2 top-1/2 -translate-y-1/2 bg-red-500 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity"
                          aria-label="Delete transaction"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                        </button>
                      </div>
                    )) : (
                        <p className="text-center text-gray-500 py-8">
                          {searchQuery ? 'No matching transactions found.' : 'No transactions found.'}
                        </p>
                    )}
                </div>
            </>
        )}
        
        {view === 'subscriptions' && (
             <div className="space-y-2">
                {sortedSubscriptions.length > 0 ? sortedSubscriptions.map((sub) => (
                    <div key={sub.id} className="group relative">
                        <SubscriptionItem subscription={sub} account={accounts.find(a => a.id === sub.accountId)} onClick={() => openSubscriptionModal(sub)} formatCurrency={formatCurrency} />
                        <button 
                          onClick={() => handleDeleteSubscription(sub.id)}
                          className="absolute right-2 top-1/2 -translate-y-1/2 bg-red-500 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity"
                          aria-label="Delete subscription"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                        </button>
                    </div>
                )) : (
                    <div className="text-center text-gray-500 py-8">
                        <RepeatIcon className="w-12 h-12 mx-auto mb-2 text-gray-600"/>
                        <p>No subscriptions found.</p>
                        <p className="text-sm mt-1">Click the center '+' button to add one.</p>
                    </div>
                )}
            </div>
        )}
    </div>
  );
};


// Settings Screen
const SettingsScreen: React.FC<SettingsScreenProps> = ({ accounts, openAccountModal, handleDeleteAccount, currency, setCurrency }) => {
    const { logout, user } = useAuth();
    
    return (
        <div className="animate-fade-in">
            <h1 className="text-2xl font-bold mb-4 text-gray-100">Settings</h1>
            
            <div className="mb-6 p-4 bg-gray-900 rounded-xl border border-gray-800">
                <p className="text-sm text-gray-400">Logged in as</p>
                <p className="font-semibold text-gray-200">{user?.email}</p>
            </div>

            <div className="bg-gray-900 rounded-xl border border-gray-800">
                <div className="p-4 border-b border-gray-800 flex justify-between items-center">
                    <h2 className="font-semibold text-gray-200">Accounts</h2>
                    <button onClick={() => openAccountModal()} className="bg-indigo-600 text-white px-3 py-1 rounded-lg text-sm font-semibold hover:bg-indigo-500 transition-colors">Add New</button>
                </div>
                <ul className="divide-y divide-gray-800">
                    {accounts.map((acc) => (
                        <li key={acc.id} className="p-4 flex justify-between items-center group">
                            <div>
                                <p className="font-medium text-gray-200">{acc.name}</p>
                                <p className="text-sm text-gray-400">{acc.type}</p>
                            </div>
                            <div className="flex items-center space-x-4 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button onClick={() => openAccountModal(acc)} className="text-indigo-400 text-sm font-semibold">Edit</button>
                                <button onClick={() => handleDeleteAccount(acc.id)} className="text-red-400 text-sm font-semibold">Delete</button>
                            </div>
                        </li>
                    ))}
                     {accounts.length === 0 && <li className="p-4 text-center text-sm text-gray-500">No accounts created.</li>}
                </ul>
            </div>
            <div className="mt-6 bg-gray-900 rounded-xl border border-gray-800 p-4">
                <label htmlFor="currency-select" className="block font-semibold text-gray-200 mb-2">Currency</label>
                <select 
                    id="currency-select" 
                    value={currency} 
                    onChange={e => setCurrency(e.target.value as Currency)}
                    className="w-full bg-gray-800 border-gray-700 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white"
                >
                    {Object.values(Currency).map(c => <option key={c} value={c}>{c}</option>)}
                </select>
            </div>
            <div className="mt-6">
                <button 
                    onClick={logout} 
                    className="w-full flex items-center justify-center space-x-2 bg-gray-800 text-red-400 font-semibold py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors"
                >
                    <LogoutIcon className="w-5 h-5" />
                    <span>Logout</span>
                </button>
            </div>
        </div>
    );
};


// Bottom Navigation
const BottomNav = ({ activeTab, setActiveTab, onScanClick, onAddClick }: { activeTab: string, setActiveTab: (tab: string) => void, onScanClick: () => void, onAddClick: () => void }) => {
  const navItems = [
    { id: 'dashboard', label: 'Home', icon: HomeIcon },
    { id: 'transactions', label: 'Transactions', icon: ListIcon },
    { id: 'add', label: 'Add', icon: PlusIcon, isCentral: true, action: onAddClick },
    { id: 'scan', label: 'Scan', icon: CameraIcon, action: onScanClick },
    { id: 'settings', label: 'Settings', icon: CogIcon },
  ];

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-gray-900/80 backdrop-blur-sm border-t border-gray-800">
      <nav className="flex justify-around items-center max-w-md mx-auto h-16">
        {navItems.map(item => {
          if (item.isCentral) {
            return (
              <button key={item.id} onClick={item.action} className="-mt-8 bg-indigo-600 text-white rounded-full p-4 shadow-lg hover:bg-indigo-500 transition-transform transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500">
                <item.icon className="w-7 h-7" />
              </button>
            );
          }
          return (
            <button
              key={item.id}
              onClick={() => item.action ? item.action() : setActiveTab(item.id)}
              className={`flex flex-col items-center justify-center w-full transition-colors duration-200 ${activeTab === item.id ? 'text-indigo-400' : 'text-gray-400 hover:text-indigo-400'}`}
            >
              <item.icon className="w-6 h-6 mb-1" />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          );
        })}
      </nav>
    </footer>
  );
};

// TransactionItem Component
interface TransactionItemProps {
  transaction: Transaction;
  account?: Account;
  onClick: () => void;
  formatCurrency: (amount: number) => string;
}
const TransactionItem: React.FC<TransactionItemProps> = ({ transaction, account, onClick, formatCurrency }) => {
    const isIncome = transaction.type === TransactionType.Income;
    const sign = isIncome ? '+' : '-';
    const color = isIncome ? 'text-green-400' : 'text-gray-200';
    const Icon = isIncome ? ArrowDownIcon : ArrowUpIcon;
    const iconColor = isIncome ? 'bg-green-500/10 text-green-400' : 'bg-red-500/10 text-red-400';

    return (
        <button onClick={onClick} className="w-full text-left bg-gray-900 p-3 rounded-xl border border-gray-800 flex items-center space-x-4 hover:bg-gray-800 transition-colors duration-200">
            <div className={`rounded-full p-2 ${iconColor}`}>
                <Icon className="w-5 h-5" />
            </div>
            <div className="flex-1">
                <p className="font-semibold text-gray-200">{transaction.description}</p>
                <p className="text-sm text-gray-400">{new Date(transaction.date).toLocaleDateString()} &bull; {transaction.category}</p>
            </div>
            <div className="text-right">
                <p className={`font-semibold font-mono ${color}`}>{sign}{formatCurrency(transaction.amount).replace(/[^0-9.,-]/g, '')}</p>
                {account && <p className="text-sm text-gray-500">{account.name}</p>}
            </div>
        </button>
    );
};

// SubscriptionItem Component
const SubscriptionItem: React.FC<{ subscription: Subscription; account?: Account; onClick: () => void; formatCurrency: (amount: number) => string; }> = ({ subscription, account, onClick, formatCurrency }) => {
    return (
        <button onClick={onClick} className="w-full text-left bg-gray-900 p-3 rounded-xl border border-gray-800 flex items-center space-x-4 hover:bg-gray-800 transition-colors duration-200">
            <div className={`rounded-full p-2 bg-purple-500/10 text-purple-400`}>
                <RepeatIcon className="w-5 h-5" />
            </div>
            <div className="flex-1">
                <p className="font-semibold text-gray-200">{subscription.description}</p>
                <p className="text-sm text-gray-400">{`Next on ${new Date(subscription.startDate).toLocaleDateString()}`}</p>
            </div>
            <div className="text-right">
                <p className={`font-semibold font-mono text-gray-200`}>{formatCurrency(subscription.amount)}</p>
                {account && <p className="text-sm text-gray-500">{account.name}</p>}
            </div>
        </button>
    );
};


// Scan Modal
const ScanModal = ({ accounts, onClose, onScanComplete }: { accounts: Account[]; onClose: () => void; onScanComplete: (data: GeminiScanResult, accountId: string) => void; }) => {
  const { user } = useAuth();
  const [image, setImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState('Analyzing...');
  const [error, setError] = useState<string | null>(null);
  const [selectedAccountId, setSelectedAccountId] = useState<string>(accounts[0]?.id || '');
  
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleScan = async () => {
    if (!imageFile || !selectedAccountId || !user) {
      setError("Please select an image and an account.");
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      setLoadingText('Uploading...');
      const result = await scanReceiptWithFirebase(imageFile, user.uid, (progress) => {
          if (progress < 100) {
              setLoadingText(`Uploading... ${Math.round(progress)}%`);
          } else {
              setLoadingText('Analyzing...');
          }
      });
      onScanComplete(result, selectedAccountId);
    } catch (e: any) {
      setError(e.message || "An unknown error occurred.");
    } finally {
      setIsLoading(false);
    }
  };
  
  const ModalWrapper: React.FC<{children: React.ReactNode}> = ({children}) => (
      <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
          <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm p-6 text-center border border-gray-700">
              {children}
          </div>
      </div>
  );

  if (accounts.length === 0) {
      return (
        <ModalWrapper>
            <h2 className="text-xl font-bold mb-2 text-yellow-400">No Account Found</h2>
            <p className="text-gray-400 mb-4">Please add an account in Settings before scanning a receipt.</p>
            <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">Close</button>
        </ModalWrapper>
      );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
      <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
        <div className="p-6">
          <h2 className="text-xl font-bold text-center mb-4 text-gray-100">Scan Receipt</h2>
          {error && <p className="bg-red-500/10 text-red-400 p-3 rounded-lg text-sm mb-4">{error}</p>}
          
          <div className="mb-4">
              <label htmlFor="account" className="block text-sm font-medium text-gray-400 mb-1">Deposit to Account</label>
              <select id="account" value={selectedAccountId} onChange={e => setSelectedAccountId(e.target.value)} className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                  {accounts.map(acc => <option key={acc.id} value={acc.id}>{acc.name}</option>)}
              </select>
          </div>
          
          {image ? (
            <div className="mb-4">
              <img src={image} alt="Receipt preview" className="rounded-lg max-h-60 w-full object-contain mb-2" />
              <button onClick={() => { setImage(null); setImageFile(null); if(fileInputRef.current) fileInputRef.current.value = ""; }} className="w-full text-sm text-indigo-400 hover:underline">Choose another</button>
            </div>
          ) : (
            <div className="mb-4">
              <input type="file" accept="image/*" capture="environment" onChange={handleFileChange} className="hidden" ref={fileInputRef} />
              <button onClick={() => fileInputRef.current?.click()} className="w-full border-2 border-dashed border-gray-600 rounded-lg p-8 flex flex-col items-center justify-center text-gray-400 hover:bg-gray-700/50 hover:border-indigo-500 transition-colors">
                <CameraIcon className="w-10 h-10 mb-2" />
                <span className="font-semibold">Capture or Upload</span>
              </button>
            </div>
          )}

          <div className="space-y-2">
            <button onClick={handleScan} disabled={!image || isLoading} className="w-full bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 disabled:bg-indigo-400/50 disabled:cursor-not-allowed flex items-center justify-center transition-colors">
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                  {loadingText}
                </>
              ) : "Process Receipt"}
            </button>
            <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  );
};


// Transaction Modal
interface TransactionModalProps {
  accounts: Account[];
  transaction: Transaction | Partial<Transaction> | null;
  onSave: (transaction: Omit<Transaction, 'id'> | Transaction) => void;
  onClose: () => void;
  currency: Currency;
}
const TransactionModal: React.FC<TransactionModalProps> = ({ accounts, transaction, onSave, onClose, currency }) => {
    const isEditing = !!transaction?.id;
    const [txData, setTxData] = useState({
        accountId: transaction?.accountId || accounts[0]?.id || '',
        type: transaction?.type || TransactionType.Expense,
        amount: transaction?.amount || '',
        category: transaction?.category || '',
        description: transaction?.description || '',
        date: transaction?.date ? new Date(transaction.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    });

    const handleChange = <K extends keyof typeof txData,>(key: K, value: (typeof txData)[K]) => {
        setTxData(prev => ({...prev, [key]: value}));
    };

    const handleTypeChange = (type: TransactionType) => {
        handleChange('type', type);
        handleChange('category', ''); // Reset category on type change
    };
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if(!txData.accountId || !txData.amount) {
            alert('Please select an account and enter an amount.');
            return;
        }
        onSave({ ...transaction, ...txData, amount: parseFloat(txData.amount.toString()) } as Transaction);
    };
    
    const categories = txData.type === TransactionType.Expense ? EXPENSE_CATEGORIES : INCOME_CATEGORIES;

    if (accounts.length === 0 && !txData.accountId) {
        return (
             <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
                <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm p-6 text-center border border-gray-700">
                    <h2 className="text-xl font-bold mb-2 text-yellow-400">No Account Found</h2>
                    <p className="text-gray-400 mb-4">Please add an account in Settings before adding a transaction.</p>
                    <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">Close</button>
                </div>
            </div>
        );
    }

    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
                <div className="p-6">
                    <h2 className="text-xl font-bold text-center mb-4 text-gray-100">{isEditing ? 'Edit' : 'Add'} Transaction</h2>

                    <div className="grid grid-cols-2 gap-2 mb-4 bg-gray-900 p-1 rounded-lg">
                        <button type="button" onClick={() => handleTypeChange(TransactionType.Expense)} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${txData.type === TransactionType.Expense ? 'bg-gray-700 shadow' : 'text-gray-400'}`}>Expense</button>
                        <button type="button" onClick={() => handleTypeChange(TransactionType.Income)} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${txData.type === TransactionType.Income ? 'bg-gray-700 shadow' : 'text-gray-400'}`}>Income</button>
                    </div>

                    <div className="space-y-4">
                        <div className="relative">
                            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-2xl text-gray-500">{new Intl.NumberFormat(undefined, {style:'currency', currency, minimumFractionDigits:0}).format(0).replace(/[0-9]/g, '').trim()}</span>
                            <input type="number" step="0.01" placeholder="0.00" value={txData.amount} onChange={e => handleChange('amount', e.target.value)} required className="w-full text-4xl font-bold text-center border-none focus:ring-0 p-2 bg-transparent text-white" />
                        </div>
                        <input type="text" placeholder="Description" value={txData.description} onChange={e => handleChange('description', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                        <select value={txData.category} onChange={e => handleChange('category', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                            <option value="" disabled>Select Category</option>
                            {categories.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                        </select>
                        <select value={txData.accountId} onChange={e => handleChange('accountId', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                            <option value="" disabled>Select Account</option>
                            {accounts.map(acc => <option key={acc.id} value={acc.id}>{acc.name}</option>)}
                        </select>
                        <input type="date" value={txData.date} onChange={e => handleChange('date', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                    </div>
                </div>
                <div className="bg-gray-800/50 p-4 grid grid-cols-2 gap-3 rounded-b-2xl border-t border-gray-700">
                    <button type="button" onClick={onClose} className="bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
                    <button type="submit" className="bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 transition-colors">Save</button>
                </div>
            </form>
        </div>
    );
};


// Account Modal
const AccountModal = ({ account, onSave, onClose }: { account: Account | null; onSave: (acc: Omit<Account, 'id'> | Account) => void; onClose: () => void; }) => {
    const isEditing = !!account;
    const [accData, setAccData] = useState({
        name: account?.name || '',
        type: account?.type || AccountType.Checking,
        initialBalance: account?.initialBalance ?? '',
    });

    const handleChange = (key: keyof typeof accData, value: any) => {
        setAccData(prev => ({ ...prev, [key]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSave({ ...account, ...accData, initialBalance: parseFloat(String(accData.initialBalance)) });
    };
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
                <div className="p-6">
                    <h2 className="text-xl font-bold text-center mb-6 text-gray-100">{isEditing ? 'Edit' : 'Add'} Account</h2>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-400">Name</label>
                            <input type="text" value={accData.name} onChange={e => handleChange('name', e.target.value)} required className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                        </div>
                         <div>
                            <label className="block text-sm font-medium text-gray-400">Type</label>
                            <select value={accData.type} onChange={e => handleChange('type', e.target.value)} required className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                {Object.values(AccountType).map(type => <option key={type} value={type}>{type}</option>)}
                            </select>
                        </div>
                         <div>
                            <label className="block text-sm font-medium text-gray-400">Initial Balance</label>
                            <input type="number" step="0.01" value={accData.initialBalance} onChange={e => handleChange('initialBalance', e.target.value)} required className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                        </div>
                    </div>
                </div>
                <div className="bg-gray-800/50 p-4 grid grid-cols-2 gap-3 rounded-b-2xl border-t border-gray-700">
                    <button type="button" onClick={onClose} className="bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
                    <button type="submit" className="bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 transition-colors">Save</button>
                </div>
            </form>
        </div>
    );
};

// AddChoiceModal
const AddChoiceModal = ({ onClose, onAddTransaction, onAddSubscription }: { onClose: () => void; onAddTransaction: () => void; onAddSubscription: () => void; }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-end justify-center z-50 p-4 animate-fade-in" onClick={onClose}>
        <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700" onClick={e => e.stopPropagation()}>
            <div className="p-4 space-y-3">
                <button onClick={onAddTransaction} className="w-full flex items-center text-left p-4 bg-gray-900 hover:bg-gray-700 rounded-lg transition-colors">
                    <ReceiptIcon className="w-6 h-6 mr-4 text-indigo-400" />
                    <div>
                        <p className="font-semibold text-gray-100">New Transaction</p>
                        <p className="text-sm text-gray-400">Log a single expense or income.</p>
                    </div>
                </button>
                 <button onClick={onAddSubscription} className="w-full flex items-center text-left p-4 bg-gray-900 hover:bg-gray-700 rounded-lg transition-colors">
                    <RepeatIcon className="w-6 h-6 mr-4 text-purple-400" />
                    <div>
                        <p className="font-semibold text-gray-100">New Subscription</p>
                        <p className="text-sm text-gray-400">Add a recurring weekly/monthly bill.</p>
                    </div>
                </button>
            </div>
            <div className="p-2 border-t border-gray-700">
                 <button onClick={onClose} className="w-full bg-transparent text-gray-300 font-semibold py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">Cancel</button>
            </div>
        </div>
    </div>
  );
};

// Subscription Modal
const SubscriptionModal = ({ accounts, subscription, onSave, onClose, currency }: { accounts: Account[]; subscription: Subscription | null; onSave: (sub: Omit<Subscription, 'id'> | Subscription) => void; onClose: () => void; currency: Currency; }) => {
    const isEditing = !!subscription;
    const [subData, setSubData] = useState({
        accountId: subscription?.accountId || accounts[0]?.id || '',
        amount: subscription?.amount || '',
        category: subscription?.category || '',
        description: subscription?.description || '',
        frequency: subscription?.frequency || SubscriptionFrequency.Monthly,
        startDate: subscription?.startDate ? new Date(subscription.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    });

    const handleChange = <K extends keyof typeof subData,>(key: K, value: (typeof subData)[K]) => {
        setSubData(prev => ({...prev, [key]: value}));
    };
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if(!subData.accountId || !subData.amount) {
            alert('Please select an account and enter an amount.');
            return;
        }
        onSave({ ...subscription, ...subData, amount: parseFloat(subData.amount.toString()) } as Subscription);
    };

    if (accounts.length === 0 && !subData.accountId) {
        return (
             <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
                <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm p-6 text-center border border-gray-700">
                    <h2 className="text-xl font-bold mb-2 text-yellow-400">No Account Found</h2>
                    <p className="text-gray-400 mb-4">Please add an account in Settings before adding a subscription.</p>
                    <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">Close</button>
                </div>
            </div>
        );
    }
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
                <div className="p-6">
                    <h2 className="text-xl font-bold text-center mb-4 text-gray-100">{isEditing ? 'Edit' : 'Add'} Subscription</h2>
                    <div className="space-y-4">
                        <div className="relative">
                            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-2xl text-gray-500">{new Intl.NumberFormat(undefined, {style:'currency', currency, minimumFractionDigits:0}).format(0).replace(/[0-9]/g, '').trim()}</span>
                            <input type="number" step="0.01" placeholder="0.00" value={subData.amount} onChange={e => handleChange('amount', e.target.value)} required className="w-full text-4xl font-bold text-center border-none focus:ring-0 p-2 bg-transparent text-white" />
                        </div>
                        <input type="text" placeholder="Description (e.g. Netflix)" value={subData.description} onChange={e => handleChange('description', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                        <select value={subData.category} onChange={e => handleChange('category', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                            <option value="" disabled>Select Category</option>
                            {EXPENSE_CATEGORIES.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                        </select>
                        <select value={subData.accountId} onChange={e => handleChange('accountId', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                            <option value="" disabled>Select Account</option>
                            {accounts.map(acc => <option key={acc.id} value={acc.id}>{acc.name}</option>)}
                        </select>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-xs font-medium text-gray-400 mb-1">Frequency</label>
                                <select value={subData.frequency} onChange={e => handleChange('frequency', e.target.value as SubscriptionFrequency)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                    {Object.values(SubscriptionFrequency).map(freq => <option key={freq} value={freq}>{freq}</option>)}
                                </select>
                            </div>
                            <div>
                                <label className="block text-xs font-medium text-gray-400 mb-1">Next Payment</label>
                                <input type="date" value={subData.startDate} onChange={e => handleChange('startDate', e.target.value)} required title="Next Payment Date" className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="bg-gray-800/50 p-4 grid grid-cols-2 gap-3 rounded-b-2xl border-t border-gray-700">
                    <button type="button" onClick={onClose} className="bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
                    <button type="submit" className="bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 transition-colors">Save</button>
                </div>
            </form>
        </div>
    );
};
